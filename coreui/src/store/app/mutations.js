import io from "socket.io-client";

export default {
  setApiToken(state, token) {
    axios.defaults.headers.common["Authorization"] = `Bearer ${token}`;
    localStorage.setItem("api_token",token)
  },
  setVApp(state, value) {
    state.vApp = value;
  },
  setProgressBar(state, value) {
    state.progressBar = value;
  },
  setOnlineChannel(state, value) {
    state.onlineChannel = io(value);
  },
  setLocation(state, value) {
    state.position = value
    axios.defaults.headers.common["ll"] = state.position.lat
    axios.defaults.headers.common["lg"] = state.position.lng
    axios.defaults.headers.common["accuracy"] = state.position.accuracy
  },
  setMapKey(statue, value) {
    statue.googleMapsKey = value
    // localStorage.setItem('map-key', value);
  }
};
