export default {
  loadVApp({commit}) {
    commit("setVApp", true);
  },
  unLoadVApp({commit}) {
    commit("setVApp", false);
  },
  loadProgressBar({commit}) {
    commit("setProgressBar", true);
  },
  unLoadProgressBar({commit}) {
    commit("setProgressBar", false);
  },
  loadOnlineChannel({commit}, value) {
    commit("setOnlineChannel", value);
  },
  loadLocation({commit}) {
    if ("geolocation" in navigator) {
      let id;
      let options;

      function success(pos) {
        const crd = pos.coords;
        commit("setLocation", {lat: crd.latitude, lng: crd.longitude,accuracy: crd.accuracy})
      }

      function error(err) {
        console.error(`ERROR(${err.code}): ${err.message}`);
      }

      options = {
        enableHighAccuracy: true,
        maximumAge: 0
      };

      navigator.geolocation.watchPosition(success, error, options);

    }
  },
  async loadGoogleMapsKey({state, commit}) {
    if (state.googleMapsKey === '') {
      const res = await axios.get("/api/maps/credentials")
      commit('setMapKey', res.data)
    }
  }
};
