/**
 * Large Dataset Handler Mixin
 * 
 * Provides comprehensive error handling and performance optimization
 * for Vue components dealing with large datasets (10K+ records)
 * 
 * Features:
 * - Memory monitoring and cleanup
 * - Progressive loading with user feedback
 * - Error recovery mechanisms
 * - Browser performance optimization
 * - Component context preservation
 */

export default {
  data() {
    return {
      // Large dataset handling state
      largeDatasetState: {
        isProcessing: false,
        progress: 0,
        currentOperation: '',
        memoryWarningShown: false,
        lastMemoryCheck: 0,
        processingStartTime: null,
        maxSafeRecords: 100000,
        chunkSize: 5000,
        processingTimeout: null
      }
    }
  },

  computed: {
    // Check if current dataset is considered large
    isLargeDataset() {
      return this.items && this.items.length > 10000;
    },

    // Check if dataset is extremely large
    isExtremelyLargeDataset() {
      return this.items && this.items.length > 100000;
    },

    // Get memory usage if available
    currentMemoryUsage() {
      if (!performance.memory) return null;
      
      const memory = performance.memory;
      return {
        used: Math.round(memory.usedJSHeapSize / (1024 * 1024)),
        total: Math.round(memory.totalJSHeapSize / (1024 * 1024)),
        limit: Math.round(memory.jsHeapSizeLimit / (1024 * 1024)),
        percentage: Math.round((memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100)
      };
    }
  },

  methods: {
    /**
     * Initialize large dataset processing with error handling
     */
    initializeLargeDatasetProcessing(operation = 'Processing') {
      try {
        this.largeDatasetState.isProcessing = true;
        this.largeDatasetState.progress = 0;
        this.largeDatasetState.currentOperation = operation;
        this.largeDatasetState.processingStartTime = Date.now();
        
        // Clear any existing timeout
        if (this.largeDatasetState.processingTimeout) {
          clearTimeout(this.largeDatasetState.processingTimeout);
        }
        
        // Set a safety timeout to prevent infinite processing
        this.largeDatasetState.processingTimeout = setTimeout(() => {
          this.handleLargeDatasetTimeout();
        }, 300000); // 5 minutes timeout
        
        // Monitor memory usage
        this.monitorMemoryUsage();
        
        return true;
      } catch (error) {
        console.error('Failed to initialize large dataset processing:', error);
        this.handleLargeDatasetError(error, 'initialization');
        return false;
      }
    },

    /**
     * Finalize large dataset processing
     */
    finalizeLargeDatasetProcessing() {
      try {
        this.largeDatasetState.isProcessing = false;
        this.largeDatasetState.progress = 0;
        this.largeDatasetState.currentOperation = '';
        
        // Clear timeout
        if (this.largeDatasetState.processingTimeout) {
          clearTimeout(this.largeDatasetState.processingTimeout);
          this.largeDatasetState.processingTimeout = null;
        }
        
        // Calculate processing time
        if (this.largeDatasetState.processingStartTime) {
          const processingTime = Date.now() - this.largeDatasetState.processingStartTime;
          console.log(`Large dataset processing completed in ${processingTime}ms`);
        }
        
        // Force garbage collection if available
        this.forceGarbageCollection();
        
      } catch (error) {
        console.error('Error finalizing large dataset processing:', error);
      }
    },

    /**
     * Update processing progress with error handling
     */
    updateLargeDatasetProgress(progress, operation = null) {
      try {
        this.largeDatasetState.progress = Math.min(100, Math.max(0, progress));
        
        if (operation) {
          this.largeDatasetState.currentOperation = operation;
        }
        
        // Check memory usage periodically
        if (Date.now() - this.largeDatasetState.lastMemoryCheck > 5000) {
          this.monitorMemoryUsage();
        }
        
      } catch (error) {
        console.error('Error updating progress:', error);
      }
    },

    /**
     * Monitor memory usage and show warnings
     */
    monitorMemoryUsage() {
      try {
        const memory = this.currentMemoryUsage;
        if (!memory) return;
        
        this.largeDatasetState.lastMemoryCheck = Date.now();
        
        // Show warning if memory usage is high
        if (memory.percentage > 80 && !this.largeDatasetState.memoryWarningShown) {
          this.largeDatasetState.memoryWarningShown = true;
          this.flash(
            `High memory usage detected (${memory.percentage}%). Consider reducing dataset size.`,
            'warning'
          );
        }
        
        // Force cleanup if memory is critically high
        if (memory.percentage > 90) {
          this.performEmergencyCleanup();
        }
        
      } catch (error) {
        console.error('Error monitoring memory:', error);
      }
    },

    /**
     * Handle large dataset processing timeout
     */
    handleLargeDatasetTimeout() {
      console.warn('Large dataset processing timeout');
      
      this.flash(
        'Processing is taking longer than expected. The operation has been cancelled for performance reasons.',
        'warning'
      );
      
      this.finalizeLargeDatasetProcessing();
      this.performEmergencyCleanup();
    },

    /**
     * Handle errors specific to large dataset operations
     */
    handleLargeDatasetError(error, context = 'unknown') {
      console.error(`Large dataset error in ${context}:`, error);
      
      // Finalize processing state
      this.finalizeLargeDatasetProcessing();
      
      // Handle specific error types
      if (error.name === 'RangeError' || error.message.includes('memory')) {
        this.handleMemoryError(error);
      } else if (error.message.includes('$emit') || error.message.includes('Cannot read properties')) {
        this.handleComponentContextError(error);
      } else if (error.message.includes('Maximum call stack')) {
        this.handleStackOverflowError(error);
      } else {
        this.handleGenericLargeDatasetError(error, context);
      }
      
      // Perform cleanup
      this.performEmergencyCleanup();
    },

    /**
     * Handle memory-related errors
     */
    handleMemoryError(error) {
      this.flash(
        'Memory limit reached. Try reducing the dataset size by applying filters or exporting in smaller batches.',
        'error'
      );
      
      // Suggest alternatives
      this.suggestDatasetReductionStrategies();
    },

    /**
     * Handle component context errors (like $emit issues)
     */
    handleComponentContextError(error) {
      this.flash(
        'Component context lost during processing. Please refresh the page and try with a smaller dataset.',
        'error'
      );
      
      // Attempt to restore component state
      this.attemptComponentStateRecovery();
    },

    /**
     * Handle stack overflow errors
     */
    handleStackOverflowError(error) {
      this.flash(
        'Processing stack overflow. The dataset is too large for browser processing. Try server-side export.',
        'error'
      );
    },

    /**
     * Handle generic large dataset errors
     */
    handleGenericLargeDatasetError(error, context) {
      this.flash(
        `Error during ${context}: ${error.message}. Try reducing the dataset size.`,
        'error'
      );
    },

    /**
     * Suggest strategies to reduce dataset size
     */
    suggestDatasetReductionStrategies() {
      const strategies = [
        'Apply date range filters',
        'Filter by specific divisions or employees',
        'Export data in monthly batches',
        'Use CSV export for large datasets',
        'Contact support for server-side processing'
      ];
      
      this.flash(`Suggestions: ${strategies.join('; ')}`, 'info');
    },

    /**
     * Attempt to recover component state
     */
    attemptComponentStateRecovery() {
      try {
        // Reset processing state
        this.largeDatasetState.isProcessing = false;
        this.largeDatasetState.progress = 0;
        
        // Clear selections to reduce memory usage
        if (this.selected && this.selected.length > 1000) {
          this.selected = [];
          this.checkAllAccounts = false;
        }
        
        // Force Vue reactivity update
        this.$forceUpdate();
        
      } catch (recoveryError) {
        console.error('Component state recovery failed:', recoveryError);
        this.flash('Please refresh the page to continue.', 'warning');
      }
    },

    /**
     * Perform emergency cleanup
     */
    performEmergencyCleanup() {
      try {
        // Clear large arrays
        if (this.selected && this.selected.length > 10000) {
          this.selected = [];
        }
        
        // Reset processing state
        this.largeDatasetState.isProcessing = false;
        this.largeDatasetState.progress = 0;
        this.largeDatasetState.memoryWarningShown = false;
        
        // Force garbage collection
        this.forceGarbageCollection();
        
      } catch (error) {
        console.error('Emergency cleanup failed:', error);
      }
    },

    /**
     * Force garbage collection if available
     */
    forceGarbageCollection() {
      try {
        // Force garbage collection in development
        if (window.gc && typeof window.gc === 'function') {
          window.gc();
        }
        
        // Alternative: create and destroy large objects to trigger GC
        const temp = new Array(1000000).fill(null);
        temp.length = 0;
        
      } catch (error) {
        // Ignore GC errors
      }
    },

    /**
     * Check if operation should proceed based on dataset size
     */
    shouldProceedWithLargeDataset(itemCount, operation = 'operation') {
      if (itemCount > this.largeDatasetState.maxSafeRecords) {
        return confirm(
          `This ${operation} involves ${itemCount.toLocaleString()} records, which may cause browser performance issues. ` +
          `Consider reducing the dataset size. Continue anyway?`
        );
      }
      
      if (itemCount > 50000) {
        return confirm(
          `This ${operation} involves ${itemCount.toLocaleString()} records and may take several minutes. Continue?`
        );
      }
      
      return true;
    }
  },

  // Cleanup on component destruction
  beforeDestroy() {
    this.finalizeLargeDatasetProcessing();
    this.performEmergencyCleanup();
  }
};
