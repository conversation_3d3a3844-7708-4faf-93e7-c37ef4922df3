<template>
  <c-row>
    <c-col col="12" lg="12">
      <c-card no-header>
        <c-card-body>
          <c-form>
            <template slot="header">
              Edit Vacation Setting id: {{ $route.params.id }}
            </template>
            <div class="row">
              <div class="col-6">
                <c-input label="Name" type="text" placeholder="Name" v-model="vacation_setting.name"
                  disabled="disabled">
                  <template slot="label">
                    Name <span style="color: red">*</span>
                  </template>
                </c-input>
              </div>

              <div class="col-6">
                <c-input label="Key" type="text" placeholder="Key" v-model="vacation_setting.key" disabled="disabled">
                  <template slot="label">
                    Key <span style="color: red">*</span>
                  </template>
                </c-input>
              </div>
            </div>

            <div class="row">
              <div class="col-6">
                <c-input v-if="
                  inArray(vacation_setting.type, [
                    'text',
                    'number',
                    'email',
                    'file',
                    'url',
                  ])
                " label="Value" :type="vacation_setting.type" placeholder="Value"
                  v-model="vacation_setting.value"></c-input>

                <c-textarea v-if="vacation_setting.type == 'textarea'" label="Value" placeholder="Value"
                  v-model="vacation_setting.value"></c-textarea>

                <!-- <c-form-group
                  v-if="
                    vacation_setting.type == 'select' &&
                    vacation_setting.key == 'plan_level'
                  "
                >
                  <template #label> Value </template>
                  <template #input>
                    <v-select
                      title="Value"
                      v-model="vacation_setting.value"
                      :options="['Account', 'Doctor']"
                      placeholder="Select option"
                      class="mt-2"
                    />
                  </template>
                </c-form-group> -->
                <c-form-group v-if="
                  vacation_setting.type == 'select' &&
                  vacation_setting.key == 'vacation_disapprove_automatic'
                ">
                  <template #label> Value </template>
                  <template #input>
                    <v-select title="Value" v-model="vacation_setting.value" :options="[
                      'Yes',
                      'No',
                    ]" placeholder="Select option" class="mt-2" />
                  </template>
                </c-form-group>
                <c-form-group v-if="
                  vacation_setting.type == 'select' &&
                  vacation_setting.key == 'delete_edit_vacation'
                ">
                  <template #label> Value </template>
                  <template #input>
                    <v-select title="Value" v-model="vacation_setting.value" :options="[
                      'After Approval',
                      'Before Approval',
                      'Can not Edit or Delete',
                    ]" placeholder="Select option" class="mt-2" />
                  </template>
                </c-form-group>

                <c-form-group v-if="
                  vacation_setting.type == 'select' &&
                  vacation_setting.key == 'vacation_time'
                ">
                  <template #label> Value </template>
                  <template #input>
                    <v-select title="Value" v-model="vacation_setting.value" :options="[
                      'Before 4 Months',
                      'Before 3 Months',
                      'Before 2 Months',
                      'Before 1 Month',
                      'Now',
                      'After 1 Month',
                      'After 2 Months',
                    ]" placeholder="Select option" class="mt-2" />
                  </template>
                </c-form-group>
                <c-form-group v-if="
                  vacation_setting.type == 'select' &&
                  vacation_setting.key == 'vacation_approval'
                ">
                  <template #label> Value </template>
                  <template #input>
                    <v-select title="Value" v-model="vacation_setting.value" :options="[
                      'Now',
                      'After 1 Month',
                      'After 2 Months',
                      'After 3 Months',
                      'After 4 Months',
                      'After 5 Months',
                      'After 6 Months',
                    ]" placeholder="Select option" class="mt-2" />
                  </template>
                </c-form-group>
              </div>

              <div class="col-6">
                <c-form-group>
                  <template #label> Type </template>
                  <template #input>
                    <v-select title="Type" v-model="vacation_setting.type" :options="types" placeholder="Select option"
                      class="mt-2" disabled="disabled" />
                  </template>
                </c-form-group>
              </div>
            </div>
          </c-form>
        </c-card-body>
        <c-card-footer>
          <c-button color="primary" @click="update">Save</c-button>
          <c-button color="default" @click="$router.go(-1)">Cancel</c-button>
        </c-card-footer>
      </c-card>
    </c-col>
  </c-row>
</template>

<script>
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
export default {
  components: {
    vSelect,
  },
  data: () => {
    return {
      vacation_setting: {
        name: "",
        key: "",
        value: "",
        type: "text",
      },
      types: [
        "text",
        "number",
        "email",
        "date",
        "textarea",
        "map",
        "select",
        "file",
        "url",
        "checkbox",
      ]
    };
  },
  methods: {
    inArray: function (needle, haystack) {
      var length = haystack.length;
      for (var i = 0; i < length; i++) {
        if (haystack[i] == needle) return true;
      }
      return false;
    },
    initialize() {
      axios
        .get(`/api/vacationsettings/${this.$route.params.id}/edit`)
        .then((response) => {
          this.vacation_setting = response.data.vacation_settings;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    update() {
      let self = this;
      axios
        .put(`/api/vacationsettings/${this.$route.params.id}`, {
          name: this.vacation_setting.name,
          key: this.vacation_setting.key,
          value: this.vacation_setting.value,
          type: this.vacation_setting.type,
        })
        .then((response) => {
          this.flash("Vacation Setting Updated Successfully");
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
  },
  created() {
    this.initialize();
  },
};
</script>
