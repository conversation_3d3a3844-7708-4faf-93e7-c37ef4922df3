<template>
  <c-col col="12" lg="12">
    <filter-data @getSchedule="filter" />
    <c-card v-if="items.length != 0">
      <c-card-header>
        <h3 class="text-center">
          Customer Visits Report for Line: {{ items[0].line }} <br />
        </h3>
      </c-card-header>
      <c-card-body>
        <c-data-table ref="content" hover header tableFilter striped sorter footer :items="items" :fields="fields"
          :items-per-page="1000" :active-page="1" :responsive="true" pagination thead-top id="print">
          <template slot="thead-top">
            <td style="border-top: none"><strong>Total</strong></td>
            <td style="border-top: none" class="text-xs-right">
              {{ items.length }}
            </td>
          </template>
          <template v-for="field in clickable_fields" v-slot:[field]="{ item }">
            <td :key="field">
              <h6 @click="rowClickHandler(item, field)" v-bind:style="{ cursor: 'pointer' }">
                {{ item[field] }}
              </h6>
            </td>
          </template>
          <template #show="{ item }">
            <td>
              <c-button @click="$root.$doctor('Doctor Data', item.doctor_id)" color="primary"
                class="btn-sm mt-2 mr-1"><c-icon name="cil-magnifying-glass" /></c-button>
            </td>
          </template>
          <template #division="{ item }">
            <td>
              <strong style="color: green">{{ item.division }}</strong>
            </td>
          </template>
          <template #employee="{ item }">
            <td>
              <strong style="color: green">{{ item.employee }}</strong>
            </td>
          </template>
          <template #visit_date="{ item }">
            <td @click="showMore(item.id)" v-if="!readMore[item.id]">
              {{ item.visit_date.substring(0, 30) + ".." }} <br /><span style="color: blue; cursor: pointer">show
                more</span>
            </td>
            <td @click="showLess(item.id)" v-if="readMore[item.id]">
              {{ item.visit_date }} <br />
              <span style="color: red; cursor: pointer">show less</span>
            </td>
          </template>

          <template #req_lines="{ item }">
            <td @click="showMoreReqLines(item.id)" v-if="!readMoreReqLines[item.id]">
              {{ item.req_lines.substring(0, 30) + ".." }} <br /><span style="color: blue; cursor: pointer">show
                more</span>
            </td>
            <td @click="showLessReqLines(item.id)" v-if="readMoreReqLines[item.id]">
              {{ item.req_lines }} <br />
              <span style="color: red; cursor: pointer">show less</span>
            </td>
          </template>
          <template #req_emp="{ item }">
            <td @click="showMoreReqUser(item.id)" v-if="!readMoreReqUser[item.id]">
              {{ item.req_emp.substring(0, 30) + ".." }} <br /><span style="color: blue; cursor: pointer">show
                more</span>
            </td>
            <td @click="showLessReqUser(item.id)" v-if="readMoreReqUser[item.id]">
              {{ item.req_emp }} <br />
              <span style="color: red; cursor: pointer">show less</span>
            </td>
          </template>
        </c-data-table>
      </c-card-body>
      <c-card-footer>
        <download @getPrint="print" @getxlsx="download" @getpdf="createPDF" @getcsv="downloadCsv" :fields="fields"
          :data="items" :name="name" />
      </c-card-footer>
    </c-card>
  </c-col>
</template>

<script>
import download from "../../components/download-reports/download.vue";
import filterData from "../../components/reports/CustomerVisits/filterData.vue";
import { Amiri } from "../../assets/fonts/Amiri-Regular-normal";
import { capitalize } from "../../filters";
import jsPDF from "jspdf";
import "jspdf-autotable";
export default {
  components: {
    download,
    filterData,
    capitalize,
  },
  data: () => {
    return {
      clickable_fields: [],
      items: [],
      fields: [],
      visitData: {},
      lineName: null,
      name: "Customer Visits Report",
      readMore: {},
      readMoreReqUser: {},
      readMoreReqLines: {}
    };
  },
  emits: ["downloaded"],
  methods: {
    showMore(id) {
      this.$set(this.readMore, id, true);
    },
    showLess(id) {
      this.$set(this.readMore, id, false);
    },
    showMoreReqUser(id) {
      this.$set(this.readMoreReqUser, id, true);
    },
    showLessReqUser(id) {
      this.$set(this.readMoreReqUser, id, false);
    },
    showMoreReqLines(id) {
      this.$set(this.readMoreReqLines, id, true);
    },
    showLessReqLLines(id) {
      this.$set(this.readMoreReqLines, id, false);
    },
    rowClickHandler(item, field) {
      axios
        .post(`/api/show-customer-visits`, {
          visitFilter: this.visitData,
          column: field,
          item,
        })
        .then((response) => {
          const data = response.data.data;
          this.$root.$table(this.name, data);
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    print() {
      this.$htmlToPaper("print");
    },
    download() {
      let filteredData = Object.values(this.items);
      filteredData.forEach((element) => {
        delete element["color"];
      });

      this.downloadXlsx(filteredData, this.name + ".xlsx");
      this.$emit("downloaded");
    },
    downloadCsv() {
      let filteredData = Object.values(this.items);
      filteredData.forEach((element) => {
        delete element["color"];
      });

      this.downloadXlsx(filteredData, this.name + ".csv");
      this.$emit("downloaded");
    },
    createPDF() {
      let pdfName = this.name;
      const columns = this.fields.map((field) => {
        return {
          title: capitalize(field),
          dataKey: field,
        };
      });
      const body = this.items;
      const doc = new jsPDF({ filters: ["ASCIIHexEncode"] });

      doc.addFileToVFS("Amiri-Regular.ttf", Amiri);
      doc.addFont("Amiri-Regular.ttf", "Amiri", "normal");
      doc.setFont("Amiri");
      doc.setFontSize(10);
      doc.autoTable({
        columns,
        body,
        margin: { top: 10 },
        showHead: "firstPage",
        styles: {
          lineColor: "#c7c7c7",
          lineWidth: 0,
          cellPadding: 2,
          font: "Amiri",
        },
      });
      doc.save(pdfName + ".pdf");
    },
    filter({ visitFilter }) {
      this.visitData = visitFilter;
      axios
        .post(`/api/customer-visits`, {
          visitFilter,
        })
        .then((response) => {
          this.items = response.data.data;
          this.fields = response.data.fields;
          this.clickable_fields = response.data.clickable_fields;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
  },
};
</script>
