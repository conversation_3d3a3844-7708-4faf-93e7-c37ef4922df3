<template>
  <c-col col="12" lg="12">
    <filter-data @getSchedule="filter"/>
    <c-card v-if="items.length != 0">
      <c-card-header>
        <!-- Large dataset warning -->
        <div v-if="isLargeDataset" class="alert alert-warning mb-3" role="alert">
          <i class="fas fa-exclamation-triangle"></i>
          <strong>Large Dataset:</strong> {{ items.length.toLocaleString() }} records loaded.
          Performance may be impacted. Consider applying filters to reduce the dataset size.
          <span v-if="isExtremelyLargeDataset">
            Only {{ (largeDatasetState.maxSafeRecords / 20).toLocaleString() }} records are displayed for optimal performance.
          </span>
        </div>

        <div class="row">
          <div class="col-md-3"><strong>Accounts</strong></div>
          <div class="col-md-6">
            <h3 class="text-center">
              List Report for Line: {{ items[0].line }}
            </h3>
          </div>
          <div class="col-md-3">
            <CButton v-if="listData.action != 1 && listData.action != 4 && checkPermission('edit_accounts')"
                     color="primary" @click="save" style="float: right">Save
            </CButton>
            <c-button color="danger" style="float: right" v-if="listData.action === 4" @click="
              $root
                .$confirm(
                  'Delete',
                  'If You Delete These Accounts, This will delete their Visits?',
                  {
                    color: 'red',
                    width: 290,
                    zIndex: 200,
                  }
                )
                .then((confirmed) => {
                  if (confirmed) {
                    save();
                  }
                })
              ">Save
            </c-button>
          </div>
        </div>
      </c-card-header>
      <c-card-body>
        <c-form-group>
          <template #input>
            <input class="m-1" v-if="listData.action != 1" id="checkAllAccounts" type="checkbox"
                   v-model="checkAllAccounts" title="Check All Accounts" @change="checkAllAccount"/>
            <label v-if="listData.action != 1" for="checkAllAccounts">Select All</label>
          </template>
        </c-form-group>
        <!-- Loading state for large datasets -->
        <div v-if="largeDatasetState.isProcessing" class="text-center p-4">
          <div class="spinner-border text-primary" role="status">
            <span class="sr-only">{{ largeDatasetState.currentOperation }}...</span>
          </div>
          <p class="mt-2">{{ largeDatasetState.currentOperation }} {{ items.length.toLocaleString() }} records...</p>
          <div class="progress mt-2" style="height: 20px;">
            <div class="progress-bar" role="progressbar" :style="`width: ${largeDatasetState.progress}%`"
                 :aria-valuenow="largeDatasetState.progress" aria-valuemin="0" aria-valuemax="100">
              {{ largeDatasetState.progress }}%
            </div>
          </div>
          <small class="text-muted">This may take a few moments for large datasets...</small>
        </div>

        <c-data-table v-else ref="content" hover header tableFilter striped sorter footer
                      :items="paginatedItems" :fields="fields"
                      :items-per-page="currentItemsPerPage" :active-page="1" :responsive="true" pagination thead-top
                      id="print">
          <template slot="thead-top">
            <td style="border-top: none"><strong>Total</strong></td>
            <td style="border-top: none" class="text-xs-right">
              {{ items.length }}
            </td>
          </template>
          <template #s="{ item }">
            <td>
              <input v-if="listData.action != 1" class="m-1" type="checkbox" v-model="selected" :value="item"
                     title="Check One Account"/>
            </td>
          </template>
          <!-- <template #division="{ item }">
            <td>
              <strong :style="{
                color: item.color,
              }">{{ item.division }}</strong>
            </td>
          </template> -->
          <template #line="{ item }">
            <td v-if="listData.action == 7">
              <p @click="showLineMore(item.id)" v-if="!readLineMore[item.id]"> {{ item.line.substring(0, 15) + ".." }}
                <span style="color: blue; cursor: pointer">show more</span>
              </p>
              <p @click="showLineLess(item.id)" v-if="readLineMore[item.id]">
                {{ item.line }} <br/> <span style="color: blue; cursor: pointer">show less</span></p>
            </td>
            <td v-else>
              <strong>{{ item.line }}</strong>
            </td>
          </template>
          <template #division="{ item }">
            <td v-if="listData.action == 7">
              <p :style="{
                color: item.color,
              }" @click="showDivisionMore(item.id)" v-if="!readDivisionMore[item.id]">
                {{ item.division.substring(0, 15) + ".." }}
                <span style="color: blue; cursor: pointer">show more</span>
              </p>
              <p @click="showDivisionLess(item.id)" v-if="readDivisionMore[item.id]">
                {{ item.division }} <br/> <span style="color: blue; cursor: pointer">show less</span></p>
            </td>
            <td v-else>
              <strong :style="{
                color: item.color,
              }">{{ item.division }}</strong>
            </td>
          </template>
          <template #emp="{ item }">
            <td v-if="listData.action == 7 && item.emp != ''">
              <p :style="{
                color: item.color,
              }" @click="showEmpMore(item.id)" v-if="!readEmpMore[item.id]"> {{ item.emp.substring(0, 15) + ".." }}
                <span style="color: blue; cursor: pointer">show more</span>
              </p>
              <p @click="showEmpLess(item.id)" v-if="readEmpMore[item.id]">
                {{ item.emp }} <br/> <span style="color: blue; cursor: pointer">show less</span></p>
            </td>
            <td v-else>
              <strong :style="{
                color: item.color,
              }">{{ item.emp }}</strong>
            </td>
          </template>
          <!-- <template #doctor="{ item }">
            <td>
              <strong style="color: blue; cursor: pointer"><a @click="$root.$doctor('Doctor Data', item.doctor_id)">{{
                item.doctor
                  }}</a></strong>
            </td>
          </template> -->
          <template #ucode="{ item }">
            <td v-if="listData.action == 7 && item.ucode != ''">
              <p @click="showUcodeMore(item.id)" v-if="!readUcodeMore[item.id]"> {{
                  item.ucode.substring(0, 15) + ".."
                }}
                <span style="color: blue; cursor: pointer">show more</span>
              </p>
              <p @click="showUcodeLess(item.id)" v-if="readUcodeMore[item.id]">
                {{ item.ucode }} <br/> <span style="color: blue; cursor: pointer">show less</span></p>
            </td>
            <td v-else>
              <strong>{{ item.ucode }}</strong>
            </td>
          </template>
          <template #doctor="{ item }">
            <td v-if="listData.action == 7 && item.doctor != ''">
              <p @click="showDoctorMore(item.id)" v-if="!readDoctorMore[item.id]">
                {{ item.doctor.substring(0, 15) + ".." }}
                <span style="color: blue; cursor: pointer">show more</span>
              </p>
              <p @click="showDoctorLess(item.id)" v-if="readDoctorMore[item.id]">
                {{ item.doctor }} <br/> <span style="color: blue; cursor: pointer">show less</span></p>
            </td>
            <td v-else>
              <strong>{{ item.doctor }}</strong>
            </td>
          </template>
          <template #speciality="{ item }">
            <td v-if="listData.action == 7 && item.speciality != ''">
              <p @click="showSpecialityMore(item.id)" v-if="!readSpecialityMore[item.id]">
                {{ item.speciality.substring(0, 15) + ".." }}
                <span style="color: blue; cursor: pointer">show more</span>
              </p>
              <p @click="showSpecialityLess(item.id)" v-if="readSpecialityMore[item.id]">
                {{ item.speciality }} <br/> <span style="color: blue; cursor: pointer">show less</span></p>
            </td>
            <td v-else>
              <strong>{{ item.speciality }}</strong>
            </td>
          </template>
          <template #account="{ item }">
            <td>
              <strong style="color: blue; cursor: pointer"><a
                @click="$root.$account('Account Data', item.account_id)">{{
                  item.account
                }}</a></strong>
            </td>
          </template>
          <template #actions="{ item }">
            <td>
              <div class="row justify-content-center">
                <!-- <c-button
                  v-if="item.lat != 0 && item.lng != 0"
                  color="primary"
                  class="btn-sm mt-2 mr-1"
                  @click="
                    $root.$map(`Map of Account: ${item.name}`, item.location)
                  "
                  ><CIcon class="text-white" name="marker" />
                </c-button> -->
                <c-button color="primary" v-if="item.lat != 0 && item.lng != 0" class="btn-sm mt-2 mr-1"
                          @click="getLocation(item)">
                  <CIcon class="text-white" name="marker"/>
                </c-button>
                <c-button color="warning" class="btn-sm mt-2 mr-1" v-if="checkPermission('reset_account_locations') &&
                  item.lat != null &&
                  item.lng != null
                " @click="resetLocation(item)">
                  <c-icon name="cil-loop-circular"/>
                </c-button>
                <c-button color="success" class="btn-sm mt-2 mr-1" v-if="checkPermission('edit_accounts')"
                          :to="{ name: 'EditAccount', params: { id: item.account_id } }">
                  <c-icon name="cil-pencil"/>
                </c-button>
              </div>
            </td>
          </template>
        </c-data-table>
      </c-card-body>
      <c-card-footer>
        <c-button color="primary"
                  v-if="listData.action != 1 && listData.action != 5 && listData.action != 4 && checkPermission('edit_accounts')"
                  @click="save" style="float: right">Save
        </c-button>
        <c-button color="primary"
                  v-if="listData.action == 5 && checkPermission('reset_account_locations') && selected.length > 0"
                  @click="save"
                  style="float: right">Save
        </c-button>
        <c-button color="danger" style="float: right" v-if="listData.action === 4" @click="
          $root
            .$confirm(
              'Delete',
              'If You Delete These Accounts, This will delete their Visits?',
              {
                color: 'red',
                width: 290,
                zIndex: 200,
              }
            )
            .then((confirmed) => {
              if (confirmed) {
                save();
              }
            })
          ">Save
        </c-button>
        <download @getPrint="print" @getxlsx="download" @getpdf="createPDF" @getcsv="downloadCsv" :fields="fields"
                  :data="items" :name="name"/>
      </c-card-footer>
    </c-card>
  </c-col>
</template>

<script>

import LargeDatasetHandler from '../../mixins/LargeDatasetHandler'
import download from "../../components/download-reports/download.vue";
import filterData from "../../components/reports/list/filterData.vue";
// import { Amiri } from "../../assets/fonts/Amiri-Regular-normal";
import jsPDF from "jspdf";
import "jspdf-autotable";
import {capitalize} from "../../filters";

export default {
  components: {
    download,
    filterData,
    capitalize,
  },
  data: () => {
    return {
      ...LargeDatasetHandler.data(),
      items: [],
      fields: [],
      checkAllAccounts: false,
      name: "List Report",
      listData: {},
      selected: [],
      dates: [],
      readLineMore: {},
      readDivisionMore: {},
      readEmpMore: {},
      readUcodeMore: {},
      readDoctorMore: {},
      readSpecialityMore: {},
    };
  },
  computed: {
    // Optimize pagination for large datasets
    ...LargeDatasetHandler.computed,
    currentItemsPerPage() {
      if (this.items.length > 50000) {
        return this.largeDatasetState.maxSafeRecords / 20; // 5000
      } else if (this.items.length > 10000) {
        return 2000;
      }
      return 1000;
    },

    // Paginated items to prevent Vue reactivity issues
    paginatedItems() {
      if (this.largeDatasetState.isProcessing) {
        return [];
      }

      // For very large datasets, limit what's rendered
      if (this.items.length > this.largeDatasetState.maxSafeRecords) {
        return this.items.slice(0, this.largeDatasetState.maxSafeRecords / 20);
      }

      return this.items;
    }
  },
  emits: ["downloaded"],

  // Lifecycle hooks for memory management
  beforeDestroy() {
    // Clean up large datasets to prevent memory leaks
    if (this.items.length > 10000) {
      this.items = [];
      this.selected = [];
      this.dates = [];
      this.fields = [];
    }
  },

  // Handle browser back/forward navigation
  beforeRouteLeave(_to, _from, next) {
    if (this.largeDatasetState.isProcessing) {
      const proceed = confirm('Data processing is in progress. Are you sure you want to leave?');
      if (proceed) {
        this.finalizeLargeDatasetProcessing();
        next();
      } else {
        next(false);
      }
    } else {
      next();
    }
  },

  methods: {
    ...LargeDatasetHandler.methods,
    showLineMore(id) {
      this.$set(this.readLineMore, id, true);
    },
    showLineLess(id) {
      this.$set(this.readLineMore, id, false);
    },
    showDivisionMore(id) {
      this.$set(this.readDivisionMore, id, true);
    },
    showDivisionLess(id) {
      this.$set(this.readDivisionMore, id, false);
    },
    showEmpMore(id) {
      this.$set(this.readEmpMore, id, true);
    },
    showEmpLess(id) {
      this.$set(this.readEmpMore, id, false);
    },
    showUcodeMore(id) {
      this.$set(this.readUcodeMore, id, true);
    },
    showUcodeLess(id) {
      this.$set(this.readUcodeMore, id, false);
    },
    showDoctorMore(id) {
      this.$set(this.readDoctorMore, id, true);
    },
    showDoctorLess(id) {
      this.$set(this.readDoctorMore, id, false);
    },
    showSpecialityMore(id) {
      this.$set(this.readSpecialityMore, id, true);
    },
    showSpecialityLess(id) {
      this.$set(this.readSpecialityMore, id, false);
    },
    async checkAllAccount() {
      try {
        this.selected = [];

        if (this.checkAllAccounts) {
          // Check if operation should proceed
          if (!this.shouldProceedWithLargeDataset(this.items.length, 'selection')) {
            this.checkAllAccounts = false;
            return;
          }

          // Initialize processing for large datasets
          if (this.items.length > 10000) {
            this.initializeLargeDatasetProcessing('Selecting records');
          }

          // Use batch processing for large datasets
          if (this.items.length > 50000) {
            const chunkSize = this.largeDatasetState.chunkSize;
            const totalChunks = Math.ceil(this.items.length / chunkSize);

            for (let i = 0; i < totalChunks; i++) {
              const start = i * chunkSize;
              const end = Math.min(start + chunkSize, this.items.length);
              const chunk = this.items.slice(start, end);

              this.selected.push(...chunk);

              // Update progress
              const progress = Math.round(((i + 1) / totalChunks) * 100);
              this.updateLargeDatasetProgress(progress, `Selecting chunk ${i + 1}/${totalChunks}`);

              // Allow UI to update
              await this.$nextTick();

              // Small delay to prevent browser freeze
              if (i % 10 === 0) {
                await new Promise(resolve => setTimeout(resolve, 10));
              }
            }

          } else {
            // Standard processing for smaller datasets
            for (let i in this.items) {
              this.selected.push(this.items[i]);
            }
          }

          this.flash(`Selected ${this.selected.length.toLocaleString()} records`, 'info');
        }

      } catch (error) {
        this.handleLargeDatasetError(error, 'selection');
        this.checkAllAccounts = false;
        this.selected = [];

      } finally {
        this.finalizeLargeDatasetProcessing();
      }
    },
    getLocation(account) {
      axios
        .post("/api/account-location", {
          account,
        })
        .then((response) => {
          this.location = response.data.data;
          if (this.location.lat != 0 && this.location.lng != 0) {
            this.$root.$map(`Map of Account id: ${this.location.account_id}`, [
              this.location,
            ]);
          } else {
            this.flash("There is no loaction for this visit");
          }
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    resetLocation(item) {
      axios
        .post(`/api/reset-location`, {
          item,
        })
        .then((_response) => {
          this.flash(`Account ${item.account} reset the location`);
          axios
            .post(`/api/list-report`, {
              listFilter: this.listData,
            })
            .then((response) => {
              this.items = response.data.data;
            })
            .catch((error) => {
              this.showErrorMessage(error);
            });
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    async download() {
      try {
        // Check if dataset is empty
        if (!this.items || this.items.length === 0) {
          this.flash('No data available to download', 'warning');
          return;
        }

        // Check if operation should proceed
        if (!this.shouldProceedWithLargeDataset(this.items.length, 'download')) {
          return;
        }

        // Initialize processing for large datasets
        if (this.items.length > 10000) {
          this.initializeLargeDatasetProcessing('Preparing download');
        }

        // Update progress
        this.updateLargeDatasetProgress(25, 'Validating data');

        // Prepare data with error handling
        const headers = this.items.length > 0 ? Object.keys(this.items[0]) : [];
        if (headers.length === 0) {
          throw new Error('No column headers found in data');
        }

        this.updateLargeDatasetProgress(50, 'Generating Excel file');

        // Use chunked download for large datasets
        await this.downloadStyledExcel(
          this.dates,
          this.items,
          headers,
          'List Report',
          'Employee List Data:'
        );

        this.updateLargeDatasetProgress(100, 'Download complete');

        // Success message
        this.flash(`Successfully downloaded ${this.items.length.toLocaleString()} records`, 'success');

      } catch (error) {
        this.handleLargeDatasetError(error, 'download');

      } finally {
        this.finalizeLargeDatasetProcessing();
      }
    },
    downloadCsv() {
      let filteredAccounts = Object.values(this.items);
      filteredAccounts.forEach((element) => {
        delete element["color"];
      });
      this.downloadXlsx(filteredAccounts, "List.csv");
      this.$emit("downloaded");
    },
    print() {
      this.$htmlToPaper("print");
    },
    save() {
      let data = this.selected.map((account) => {
        return {
          account_id: account.account_id,
          line_id: account.line_id,
          div_id: account.div_id,
          brick_id: account.brick_id,
          doctor_id: account.doctor_id,
        }
      });
      axios
        .post(`/api/list-report-action`, {
          action: this.listData.action,
          accounts: data,
        })
        .then((_response) => {
          this.flash("Action Saved Successfully");
          this.selected = [];
          this.checkAllAccounts = false;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    async createPDF() {
      let pdfName = this.name;
      var columns = this.fields.map((field) => {
        return {
          title: capitalize(field),
          dataKey: field,
        };
      });
      const body = this.items;
      const doc = new jsPDF({filters: ["ASCIIHexEncode"]});

      const {Amiri} = await import("../../assets/fonts/Amiri-Regular-normal");

      doc.addFileToVFS("Amiri-Regular.ttf", Amiri);
      doc.addFont("Amiri-Regular.ttf", "Amiri", "normal");
      doc.setFont("Amiri");
      doc.setFontSize(10);
      doc.autoTable({
        columns,
        body,
        margin: {top: 10},
        showHead: "firstPage",
        styles: {
          lineColor: "#c7c7c7",
          lineWidth: 0,
          cellPadding: 2,
          font: "Amiri",
        },
      });
      doc.save(pdfName + ".pdf");
    },
    async filter({listFilter}) {
      try {
        this.listData = listFilter;

        // Initialize processing
        this.initializeLargeDatasetProcessing('Loading data');

        const response = await axios.post(`/api/list-report`, {
          listFilter,
        });

        this.updateLargeDatasetProgress(50, 'Processing response');

        const data = response.data.data;
        const itemCount = data.accounts ? data.accounts.length : 0;

        // Show warning for large datasets
        if (itemCount > 50000) {
          this.flash(
            `Large dataset loaded: ${itemCount.toLocaleString()} records. ` +
            `Performance may be impacted. Consider applying additional filters.`,
            'warning'
          );
        }

        this.updateLargeDatasetProgress(75, 'Updating interface');

        // Use Vue.nextTick to ensure DOM updates don't block
        await this.$nextTick();

        this.items = data.accounts || [];
        this.dates = data.dates || [];
        this.fields = data.fields || [];

        this.updateLargeDatasetProgress(100, 'Complete');

        // Reset selections for large datasets to prevent memory issues
        if (itemCount > 10000) {
          this.selected = [];
          this.checkAllAccounts = false;
        }

        // Success message
        if (itemCount > 0) {
          this.flash(`Loaded ${itemCount.toLocaleString()} records successfully`, 'success');
        }

      } catch (error) {
        this.handleLargeDatasetError(error, 'data loading');

        // Reset data on error
        this.items = [];
        this.dates = [];
        this.fields = [];

      } finally {
        this.finalizeLargeDatasetProcessing();
      }
    },
    beforeDestroy: LargeDatasetHandler.beforeDestroy
  },
};
</script>
