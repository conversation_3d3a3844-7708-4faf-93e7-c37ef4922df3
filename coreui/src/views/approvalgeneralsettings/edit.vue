<template>
  <c-row>
    <c-col col="12" lg="12">
      <c-card no-header>
        <c-card-body>
          <c-form>
            <template slot="header">
              Edit Approval Setting id: {{ $route.params.id }}
            </template>
            <div class="row">
              <div class="col-6">
                <c-input label="Name" type="text" placeholder="Name" v-model="approval_settings.name"
                  disabled="disabled">
                  <template slot="label">
                    Name <span style="color: red">*</span>
                  </template>
                </c-input>
              </div>

              <div class="col-6">
                <c-input label="Key" type="text" placeholder="Key" v-model="approval_settings.key" disabled="disabled">
                  <template slot="label">
                    Key <span style="color: red">*</span>
                  </template>
                </c-input>
              </div>
            </div>

            <div class="row">
              <div class="col-6">
                <c-form-group v-if="
                  approval_settings.type == 'select' &&
                  (approval_settings.key == 'vacation_approval_center_flow' ||
                    approval_settings.key == 'plan_approval_center_flow' ||
                    approval_settings.key == 'actual_approval_center_flow' ||
                    approval_settings.key == 'ow_approval_center_flow' ||
                    approval_settings.key == 'expense_approval_center_flow' ||
                    approval_settings.key == 'commercial_approval_center_flow' ||
                    approval_settings.key == 'active_inactive_approval_center_flow' ||
                    approval_settings.key == 'account_request_approval_center_flow' ||
                    approval_settings.key == 'commercial_bill_approval_center_flow' ||
                    approval_settings.key == 'material_approval_center_flow' ||
                    approval_settings.key == 'change_plan_approval_center_flow' ||
                    approval_settings.key == 'pv_approval_center_flow' ||
                    approval_settings.key == 'vacation_auto_approval_days' ||
                    approval_settings.key == 'pv_auto_approval_days' ||
                    approval_settings.key == 'plan_auto_approval_days' ||
                    approval_settings.key == 'commercial_auto_approval_days'
                  )
                ">
                  <template #label> Value </template>
                  <template #input>
                    <v-select title="Value" v-model="approval_settings.value" :options="['Yes', 'No']"
                      placeholder="Select Value" class="mt-2" />
                  </template>
                </c-form-group>
              </div>
              <div class="col-6">
                <c-form-group>
                  <template #label> Type </template>
                  <template #input>
                    <v-select title="Type" v-model="approval_settings.type" :options="types" placeholder="Select Type"
                      class="mt-2" disabled="disabled" />
                  </template>
                </c-form-group>
              </div>
            </div>
          </c-form>
        </c-card-body>
        <c-card-footer>
          <c-button color="primary" @click="update">Save</c-button>
          <c-button color="default" @click="$router.go(-1)">Cancel</c-button>
        </c-card-footer>
      </c-card>
    </c-col>
  </c-row>
</template>

<script>
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
export default {
  components: {
    vSelect,
  },
  data: () => {
    return {
      approval_settings: {
        name: "",
        key: "",
        value: "",
        type: "text",
      },
      types: [
        "text",
        "number",
        "email",
        "date",
        "textarea",
        "map",
        "select",
        "file",
        "url",
        "checkbox",
      ],
    };
  },
  methods: {
    initialize() {
      axios
        .get(`/api/approval-settings/${this.$route.params.id}`)
        .then((response) => {
          this.approval_settings = response.data.data;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    update() {
      axios
        .put(`/api/approval-settings/${this.$route.params.id}`, {
          name: this.approval_settings.name,
          key: this.approval_settings.key,
          value: this.approval_settings.value,
          type: this.approval_settings.type,
        })
        .then((response) => {
          this.flash("Approval Setting Updated Successfully");
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
  },
  created() {
    this.initialize();
  },
};
</script>