<template>
  <label v-if="items.length !== 0" :title="title" :for="checkboxId" :class="labelClass" style="font-weight: bold">
    <input
      :id="checkboxId"
      :class="computedCheckboxClass"
      type="checkbox"
      v-model="isAllSelected"
      :title="title"
      @change="handleChange"
    />
    {{ label }}
  </label>
</template>

<script>
export default {
  name: "SelectAllCheckbox",
  props: {
    items: {
      type: Array,
      required: true,
    },
    value: {
      type: Boolean,
      default: false,
    },
    label: {
      type: String,
      default: "All",
    },
    title: {
      type: String,
      default: "Check All",
    },
    checkboxClass: {
      type: String,
      default: "m-1",
    },
  },
  data() {
    return {
      uniqueId: `select_all_${Math.random().toString(36).substr(2, 8)}`,
    };
  },
  computed: {
    checkboxId() {
      return this.uniqueId;
    },
    isAllSelected: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit("input", val );
      },
    },
    computedCheckboxClass() {
      return this.checkboxClass || `checkbox-${this.uniqueId}`;
    },
    labelClass() {
      return `label-${this.uniqueId}`;
    },
  },
  methods: {
    handleChange(event) {
      this.toggleAll();
      this.$emit("change", event); // <- Forward native input event
    },
    toggleAll() {
      this.isAllSelected = !this.isAllSelected;
    },
  },
};
</script>
